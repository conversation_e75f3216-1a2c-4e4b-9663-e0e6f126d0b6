export default {
    pushLockCommand: 'powerBms/pushLockCommand',
    getDevicePageList: "powerBms/getPageList",
    getProjectPageList: "project/getPageList",
    getBasicInfo: 'powerBms/getPowerBmsWithProjectSummary',
    getRealData: 'powerBms/getRealData',
    getRealCells: 'powerBms/getRealCells',
    getBmsDataColumn: 'powerBms/getBmsDataColumn',
    getBmsDataLog: 'powerBms/getBmsDataLog',
    getBmsDataPage: 'powerBms/getBmsDataPage',
    statsActiveDeviceCount: 'powerBms/statsActiveDeviceCount',
    projectDetail: 'project/detail',
    statsPowerBattUsageSummary: 'powerBatt/usage/statsPowerBattUsageSummary',
    statsDevicesStatusCount: "powerBms/statsDevicesStatusCount",
    statsDeviceSocProportion: 'powerBms/statsDeviceSocProportion',
    statsPowerBattDailyUsage: 'powerBatt/usage/statsPowerBattDailyUsage',
    getPowerBmsChargeRecordPageList: 'powerBatt/usage/getPowerBmsChargeRecordPageList',
    createProject: 'project/createProject',
    editProject: 'project/editProject',
    addDeviceToProject: 'project/addDeviceToProject',
    getProjectAndBatteryCounts: 'project/getProjectAndBatteryCounts',
    batchAddBattery: 'powerBms/batchAddBattery',
    batchImportBattery: 'powerBms/batchImportBattery',
    getCellRangeAndVariance: 'powerBms/getCellRangeAndVariance',
    getStatsDeviceSocAndWorkStatusByDay: 'powerBms/statsDeviceSocAndWorkStatusByDay',
    getSupplierBmsSummary: 'powerBms/getSupplierBmsSummary',
    statsPowerBattDurUsageSummary: 'powerBatt/usage/statsPowerBattDurUsageSummary',
    statsDeviceChargeRank: 'powerBatt/usage/statsDeviceChargeRank',
    statisticDeviceUseTimeDistribution: "powerBms/statisticDeviceUseTimeDistribution",
    statisticDeviceRunTimeDistribution: "powerBms/statisticDeviceRunTimeDistribution",
    powerGetStatisticalCard: 'powerDeviceAlarm/getStatisticalCard',
    powerGetStatisticalSummary: 'powerDeviceAlarm/getStatisticalSummary',
    powerDeviceAlarmPage: 'powerDeviceAlarm/page',
    powerDeviceAlarmDetail: 'powerDeviceAlarm/detail',
    addDictItem: 'common/addDictItem',
    statsDeviceChargeTimeDistribution: 'powerBms/statsDeviceChargeTimeDistribution',
    statsDeviceChargeStartSocDistribution: 'powerBms/statsDeviceChargeStartSocDistribution',
    statsDeviceChargeCapacityDistribution: 'powerBms/statsDeviceChargeCapacityDistribution',
    getReginDeviceTree: "powerBms/getReginDeviceTree",
    bindDevice: "",
    getEnableCustomerList: "",
    deleteProject: "project/delete",
    getSimplePageList: "powerBms/getSimplePageList",
    batchBindCustomer: "powerBms/batchBindCustomer",
    delBms: 'powerBms/delBms',
    getCustomerBmsSummary: "powerBms/getCustomerBmsSummary",
    getProjectBasicInfo: 'project/getBasicInfo',
    batchDownloadQrCode:"powerBms/batchDownloadQrCode",
    batchRemarkName:"powerBms/batchRemarkName"
}

