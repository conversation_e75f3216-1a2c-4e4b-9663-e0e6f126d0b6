import store from "@/store";
import router, { resetRouter } from './router'
import notification from 'ant-design-vue/es/notification'
import Cookies from 'js-cookie'
const allowList = ['login', 'register', 'registerResult', 'agreement'] // no redirect allowList
const ACCESS_TOKEN = 'Access-Token'
const loginRoutePath = '/login'
const LoginRoutePath = '/Login'
let deviceHome = '/device'
import { whiteRouteList } from "@/utils/whiteRouteList.js"
// const devicePaths =['/device']
// const carPaths =['']
const businessType = localStorage.getItem('businessType')
router.beforeEach((to, from, next) => {
  // NProgress.start() // start progress bar
  // to.meta && typeof to.meta.title !== 'undefined' && setDocumentTitle(`${i18nRender(to.meta.title)} - ${domTitle}`)
  /* has token */
  if (whiteRouteList.includes(to.path)) {
    next()
    return
  } else {
    const token = Cookies.get(
      'token'
    )
    if (businessType == 'energy_storage_cabinet') {
      deviceHome = '/device'
    } else {
      deviceHome = '/car'
    }
    let homeRoute = localStorage.getItem('homeRoute') || deviceHome
    if (token) {
      // 设置活跃系统标识
      if (to.path == '/device' || to.path == '/device/deviceDetail') {
        localStorage.setItem('activeSystem', 'device')
      } else if (to.path == '/carSystem' || to.path == '/car' || to.path == '/carSystem/detail') {
        localStorage.setItem('activeSystem', 'car')
      }

      // 如果访问根路径或登录页面，直接跳转到deviceHome
      if (to.path === '/' || to.path === loginRoutePath || to.path === LoginRoutePath) {
        next({ path: deviceHome })
      } else {
        // 其他情况正常通过
        next()
      }
    } else {
      if (allowList.includes(to.name)) {
        // 在免登录名单，直接进入
        next()
      } else {
        next({ path: LoginRoutePath, query: { redirect: to.fullPath } })
        // NProgress.done() // if current page is login will not trigger afterEach hook, so manually handle it
      }
    }
  }
})