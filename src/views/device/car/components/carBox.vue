<template>
    <div
        class="bg-ff dark:bg-transparent rounded device-box p-3.5 cursor-pointer"
    >
        <div class="flex justify-between gap-x-2 mb-4">
            <div class="w-36 h-27 overflow-hidden rounded">
                <img :src="imgPic" class="w-full h-full" alt="" srcset="" />
            </div>
            <div class="flex-1 pt-1">
                <div class="flex justify-between items-center">
                    <div
                        class="text-xs leading-5 text-secondar-text dark:text-60-dark text-left"
                    >
                        {{ $t('No') }}：{{ data.stationNo || '-' }}
                    </div>
                </div>
                <div
                    class="overflow mb-2 text-base leading-5 font-medium text-left text-title dark:text-title-dark"
                    :title="data.stationName"
                >
                    {{ data.stationName }}
                </div>
                <div
                    class="flex items-center mb-2.5 gap-x-3"
                    style="font-size: 13px"
                >
                    <div class="device-num-tag online">
                        <span>{{ $t('Vehicles') }}：</span
                        ><span>{{ data.deviceQuantity }}</span
                        ><span>{{ $t('common_liang') }}</span>
                    </div>
                    <div
                        class="device-num-tag offline"
                        v-if="data.offlineDeviceQuantity"
                    >
                        <span>{{ $t('car_lixiancheliang') }}：</span
                        ><span>{{ data.offlineDeviceQuantity }}</span
                        ><span>{{ $t('common_liang') }}</span>
                    </div>
                </div>
                <div
                    class="flex text-secondar-text dark:text-60-dark opacity-80"
                >
                    <iconSvg
                        name="ip"
                        class="w-4 h-4"
                        style="margin-top: 3px"
                    />
                    <div class="ml-1 w-0 flex-1 overflow" :title="data.address">
                        {{ data.address || '-' }}
                    </div>
                </div>
            </div>
        </div>
        <div class="flex justify-between px-2 mb-2">
            <div class="flex-1">
                <div class="text-secondar-text dark:text-60-dark">
                    {{ $t('station_zhuangjirongliang') }}
                </div>
                <div class="text-base font-medium dark:text-title-dark">
                    {{ data.installedCapacity }}
                    <span>Ah</span>
                </div>
            </div>
            <div class="h-12 w-0.5 bg-background"></div>
            <div class="flex-1 text-center">
                <div class="inline-block text-left">
                    <div class="text-secondar-text dark:text-60-dark">
                        {{ $t('station_zhuangjigonglv') }}
                    </div>
                    <div class="text-base font-medium dark:text-title-dark">
                        {{ data.installedPower }}
                        <span>kW</span>
                    </div>
                </div>
            </div>
            <div class="h-12 w-0.5 bg-background"></div>
            <div class="flex-1 text-right">
                <div class="inline-block text-left">
                    <div class="text-secondar-text dark:text-60-dark">
                        {{ $t('station_touyunriqi') }}
                    </div>
                    <div class="text-base font-medium dark:text-title-dark">
                        {{
                            data.createTime
                                ? dayjs(data.createTime).format('YYYY/MM/DD')
                                : '-'
                        }}
                    </div>
                </div>
            </div>
        </div>
        <!-- <div>{{ userType }}</div> -->
        <div
            v-if="getCompanyInfo?.orgType == 'supplier'"
            class="px-2 py-2 bg-f5f7f7 dark:bg-ffffff-dark rounded text-secondar-text dark:text-60-dark opacity-80 overflow leading-4"
        >
            <div
                class="overflow float-left text-left"
                :title="data.supplierName"
                :style="{
                    width: supplierWidth * 14 + 'px',
                }"
            >
                {{ $t('station_fuwushang') }}：{{ data.supplierName }}
            </div>
            <div
                class="overflow float-left"
                :title="data.customerName"
                style="padding-left: 2%; border-left: 1px solid #d9d9d9"
                :style="{
                    maxWidth: `calc(100% - ${supplierWidth * 14 + 'px'})`,
                }"
            >
                {{ $t('station_kehu') }}：{{ data.customerName }}
            </div>
        </div>
        <!-- <div>{{ supplierWidth }}</div> -->
    </div>
</template>

<script>
import { toRefs, watch, ref, computed } from 'vue'
import dayjs from 'dayjs'
import { useStore } from 'vuex'
import { unitConversion, alternateUnits, transformPrice } from '../../const'
export default {
    props: {
        data: {
            type: Object,
            default: () => ({}),
        },
        userType: {
            type: String,
            default: '',
        },
    },
    setup(props) {
        const { data, userType } = toRefs(props)
        const imgPic = ref('')
        watch(
            props.data,
            () => {
                // console.log('[ props.data ] >', props.data)
                if (props.data.stationPic) {
                    imgPic.value = props.data.stationPic
                } else {
                    imgPic.value = require('@/assets/device/defaultImg.png')
                }
            },
            { immediate: true, deep: true }
        )
        const supplierWidth = ref(0)
        watch(
            data,
            (val) => {
                if (val.supplierName) {
                    supplierWidth.value =
                        val.supplierName.replace(/[\u4e00-\u9fa5]/g, 'a')
                            .length + 5
                }
            },
            { immediate: true, deep: true }
        )
        const store = useStore()
        const getCompanyInfo = computed(
            () => store.getters['user/getUserInfoData']
        )
        return {
            dayjs,
            unitConversion,
            alternateUnits,
            imgPic,
            transformPrice,
            supplierWidth,
            getCompanyInfo,
        }
    },
}
</script>

<style lang="less" scoped>
.device-num-tag {
    border-radius: 2px;
    font-size: 12px;
    height: 22px;
    align-items: center;
    padding: 0 8px;
    display: flex;
    align-items: center;

    span {
        line-height: 18px;
    }
}

.online {
    color: #fd750b;
    background: rgba(253, 117, 11, 0.1);
}

.offline {
    color: #52c41a;
    background: rgba(82, 196, 26, 0.1);
}
</style>
