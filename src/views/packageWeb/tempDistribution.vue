<template>
    <div ref="chartContainer" class="distribution-container">
        <!-- -->
    </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import * as echarts from 'echarts'
import _cloneDeep from 'lodash/cloneDeep'

const { locale } = useI18n()

const props = defineProps({
    // 数据数组
    data: {
        type: Array,
        default: () => [],
    },
    // 类型：'voltage' 或 'temperature'
    type: {
        type: String,
        default: 'voltage',
        validator: (value) => ['voltage', 'temperature'].includes(value),
    },
    // Y轴数量（行数）
    yCount: {
        type: Number,
        default: 2,
    },
    // 图表宽度
    width: {
        type: [String, Number],
        default: '100%',
    },
    // 图表高度
    height: {
        type: [String, Number],
        default: 200,
    },
})

const chartContainer = ref()
let myChart = null

// 获取基础配置选项
const getBaseOptions = () => {
    const isTemperature = props.type === 'temperature'

    return {
        gradientColor: isTemperature
            ? ['#F3E49F', '#EAC391', '#C45054']
            : ['#D4F6FB', '#A4DBE6', '#6FBECE', '#5FA7BA', '#204765'],
        tooltip: {
            show: true,
            position: 'top',
            formatter: function (params) {
                const unit = isTemperature ? '°C' : 'V'
                const sensorType = isTemperature ? '温度传感器' : '电芯'

                if (locale.value === 'en') {
                    const label = isTemperature
                        ? 'Temperature Sensor'
                        : 'Battery Cell'
                    return `${label}: ${params.dataIndex + 1}<br />${
                        params.marker
                    }    ${params.value[2]} ${unit}`
                } else {
                    return `${params.dataIndex + 1}号${sensorType}<br />${
                        params.marker
                    }    ${params.value[2]} ${unit}`
                }
            },
        },
        grid: {
            height: '88px',
            width: '100%',
            top: '0%',
            left: '0%',
        },
        xAxis: {
            type: 'category',
            data: [],
            splitArea: {
                show: true,
            },
        },
        yAxis: {
            type: 'category',
            data: [],
            splitArea: {
                show: true,
            },
            show: false,
        },
        visualMap: {
            min: 0,
            max: 10,
            calculable: true,
            orient: 'horizontal',
            left: 'center',
            bottom: '0px',
            precision: 1,
            inRange: {
                color: isTemperature
                    ? ['#F3E49F', '#EAC391', '#C45054']
                    : ['#D4F6FB', '#A4DBE6', '#6FBECE', '#5FA7BA', '#204765'],
            },
            textStyle: {
                color: '#333',
            },
        },
        series: [
            {
                name: isTemperature ? '温度(°C)' : '电压(V)',
                type: 'heatmap',
                data: [],
                label: {
                    show: false,
                },
                itemStyle: {
                    color: 'transparent',
                    borderColor: 'rgba(255,255,255,1)',
                    borderWidth: 3,
                },
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowColor: 'rgba(0, 0, 0, 0.5)',
                    },
                },
            },
        ],
    }
}

// 转换数据格式
const transformData = (data, yCount) => {
    if (!data || data.length === 0) return []

    const result = []
    const xCount = Math.floor(data.length / yCount)

    // 验证数据总量是否匹配
    if (data.length !== xCount * yCount) {
        console.warn('数据总量与坐标数量不匹配，将截取匹配的部分')
    }

    // 按y轴数量分割数据
    for (let y = 0; y < yCount; y++) {
        const start = y * xCount
        const end = start + xCount
        const yData = data.slice(start, end)

        // 生成坐标数据
        yData.forEach((value, x) => {
            if (value !== null && value !== undefined) {
                result.push([x, y, value])
            }
        })
    }

    return result
}

// 更新图表
const updateChart = () => {
    if (!chartContainer.value || !props.data || props.data.length === 0) return

    const options = getBaseOptions()
    const transformedData = transformData(props.data, props.yCount)

    // 计算数据范围
    const values = transformedData.map((item) => item[2])
    const min = Math.min(...values)
    const max = Math.max(...values)

    // 生成X轴和Y轴数据
    const xCount = Math.floor(props.data.length / props.yCount)
    const xData = Array.from({ length: xCount }, (_, index) => index + 1)
    const yData = Array.from({ length: props.yCount }, () => '')
    const gHeight = props.yCount == 2 ? '88px' : '44px'
    options.grid.height = gHeight
    // 更新配置
    options.xAxis.data = xData
    options.yAxis.data = yData
    options.visualMap.min = min
    options.visualMap.max = max
    options.series[0].data = transformedData

    // 初始化或更新图表
    if (!myChart) {
        myChart = echarts.init(chartContainer.value)
    }

    myChart.setOption(options, true)
}

// 初始化图表
const initChart = async () => {
    await nextTick()
    updateChart()
}

// 监听数据变化
watch(
    () => [props.data, props.type, props.yCount],
    () => {
        updateChart()
    },
    { deep: true, immediate: true }
)

// 监听语言变化
watch(
    () => locale.value,
    () => {
        updateChart()
    }
)

onMounted(() => {
    initChart()
})

onUnmounted(() => {
    if (myChart) {
        myChart.dispose()
        myChart = null
    }
})

// 暴露方法给父组件
defineExpose({
    updateChart,
    getChartInstance: () => myChart,
})
</script>

<style lang="less" scoped>
.distribution-container {
    width: 100%;
    height: 120px;
}
</style>
