<template>
    <div class="alarm-container">
        <!-- 页面标题 -->
        <!-- <div class="page-header text-center">
            <h2>告警信息</h2>
        </div> -->

        <!-- 顶部Tab切换 -->
        <div class="tabs-header">
            <el-tabs
                v-model="activeKey"
                @tab-change="onChange"
                class="alarm-tabs"
            >
                <el-tab-pane
                    v-for="item in tabOptions"
                    :key="item.id"
                    :label="item.label"
                    :name="item.id"
                />
            </el-tabs>
        </div>

        <!-- 列表内容区域 -->
        <div class="content-container">
            <div class="list-container" ref="listContainer">
                <!-- 告警列表 -->
                <div v-for="item in list" :key="item.id" class="list-item">
                    <AlarmItem :data="item" @refresh="refresh" />
                </div>

                <!-- 加载更多指示器 -->
                <div class="load-more text-center mt-4" v-if="list.length > 0">
                    <div
                        v-if="loadMoreStatus === 'more' && isLoadingMore"
                        class="loading-indicator"
                    >
                        <el-icon class="is-loading">
                            <Loading />
                        </el-icon>
                        <span>{{ $t('Loading') }}...</span>
                    </div>
                    <span
                        v-else-if="loadMoreStatus === 'noMore'"
                        class="no-more"
                    >
                        {{ $t('null') }}
                    </span>
                </div>
                <div class="" id="scroll-trigger"></div>
                <!-- 空状态 -->
                <div class="empty" v-if="list.length === 0 && !isLoading">
                    <!-- <img src="@/static/empty.png" alt="暂无数据" /> -->
                    <div class="text">
                        {{
                            activeKey === 'A'
                                ? t('There is no abnormality')
                                : t('No abnormal historical data')
                        }}
                    </div>
                </div>

                <!-- 加载中状态 -->
                <div class="loading" v-if="isLoading && list.length === 0">
                    <el-icon class="is-loading">
                        <Loading />
                    </el-icon>
                    <span>{{ $t('Loading') }}...</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'
import AlarmItem from './alarmItem.vue'
// import apiService from '@/apiService/device'
import powerApi from '@/apiService/h5'
import { useI18n } from 'vue-i18n'
const { t, locale } = useI18n()

const route = useRoute()

// 响应式数据
const activeKey = ref('A')
const list = ref([])
const loadMoreStatus = ref('noMore')
const isLoading = ref(false)
const isLoadingMore = ref(false)
const listContainer = ref()

// Tab选项配置
const tabOptions = ref([
    {
        label: t('当前异常'),
        id: 'A',
    },
    {
        label: t('History Alarm'),
        id: 'B',
    },
])

// 告警类型映射
const alarmTypes = {
    A: 'current', // 当前异常
    B: 'history', // 历史异常
}

// 分页配置
const config = reactive({
    current: 1,
    size: 10,
    total: 0,
})

// 页面信息
const pageInfo = ref({
    sn: route.query.sn || '',
})

// Tab切换事件
const onChange = (val) => {
    console.log('切换Tab:', val)
    activeKey.value = val
    loadingRefresh(true)
}

// 获取告警数据
const getActivePage = async () => {
    const stage = alarmTypes[activeKey.value]
    return powerApi.powerDeviceAlarmPage({
        // return apiService.getWorkOrderPage({
        ...config,
        stage: stage,
        sn: pageInfo.value?.sn,
    })
}

// 获取列表数据
const listPage = async (isRefresh = false) => {
    if (isLoading.value && !isRefresh) {
        return
    }

    // 设置加载状态
    if (isRefresh) {
        isLoading.value = true
    } else {
        isLoadingMore.value = true
    }

    try {
        const {
            data: {
                data: { records, total },
            },
        } = await getActivePage()

        if (isRefresh) {
            // 刷新数据，替换原有数据
            list.value = records || []
        } else {
            // 加载更多，追加数据
            list.value = list.value.concat(records || [])
        }

        config.total = total

        // 判断是否还有更多数据
        if (config.current * config.size >= total) {
            loadMoreStatus.value = 'noMore'
        } else {
            loadMoreStatus.value = 'more'
        }
    } catch (error) {
        ElMessage.error('数据加载失败')
    } finally {
        isLoading.value = false
        isLoadingMore.value = false
    }
}

// 防抖定时器
let scrollTimer = null

// 滚动监听处理
const onScroll = () => {
    // 清除之前的定时器
    if (scrollTimer) {
        clearTimeout(scrollTimer)
    }
    const elbox = document.getElementById('scroll-trigger')
    const { scrollTop1, scrollHeight1, clientHeight1 } = elbox

    // 设置防抖，避免频繁触发
    scrollTimer = setTimeout(() => {
        if (!listContainer.value) return

        const container = listContainer.value
        const { scrollTop, scrollHeight, clientHeight } = container

        // 当滚动到距离底部50px以内时，触发加载更多
        if (scrollTop + clientHeight >= scrollHeight - 50) {
            loadMore()
        }
    }, 100) // 100ms防抖
}

// 初始化滚动监听
const initScrollListener = () => {
    window.addEventListener('scroll', onScroll, { passive: true })
}

// 移除滚动监听
const removeScrollListener = () => {
    window.removeEventListener('scroll', onScroll)
}

// 加载更多
const loadMore = () => {
    if (loadMoreStatus.value === 'more' && !isLoadingMore.value) {
        config.current = config.current + 1
        listPage(false)
    }
}

// 刷新数据
const loadingRefresh = (isRefresh = false) => {
    config.current = 1
    loadMoreStatus.value = 'more'

    // 滚动到顶部
    if (listContainer.value) {
        listContainer.value.scrollTop = 0
    }

    listPage(isRefresh)
}

// 子组件刷新回调
const refresh = (shouldRefresh = true) => {
    if (shouldRefresh) {
        loadingRefresh(true)
    }
}

// 页面挂载时
onMounted(() => {
    // 初始化数据
    loadingRefresh(true)

    // 延迟初始化滚动监听，确保DOM已渲染
    setTimeout(() => {
        initScrollListener()
    }, 500)
})

// 页面卸载时
onUnmounted(() => {
    // 移除滚动监听
    removeScrollListener()

    // 清理定时器
    if (scrollTimer) {
        clearTimeout(scrollTimer)
        scrollTimer = null
    }
})
</script>

<style lang="less" scoped>
.alarm-container {
    min-height: 100vh;
    background-color: #ffffff;
    .page-header {
        padding: 12px 0;
        h2 {
            margin: 0;
            font-size: 14px;
            font-weight: 500;
            color: #333;
        }
    }

    .tabs-header {
        border-radius: 8px 8px 0 0;
        margin-bottom: 0;
        padding: 8px 0;
        background: var(--border);

        .alarm-tabs {
            :deep(.el-tabs__header) {
                margin: 0;
            }

            :deep(.el-tabs__nav-wrap) {
                padding: 0 20px;
                &::after {
                    display: none;
                }
            }

            :deep(.el-tabs__item) {
                font-size: 14px;
                font-weight: 500;
                color: #666;
                height: 32px;

                &.is-active {
                    color: #3edacd;
                    font-weight: 600;
                }
            }
            :deep(.el-tabs__item.is-active::after) {
                border-radius: 9px;
                width: 24px !important;
                margin-left: -12px !important;
            }

            :deep(.el-tabs__active-bar) {
                background-color: #3edacd;
            }

            :deep(.el-tabs__content) {
                display: none; // 隐藏tab内容，因为我们单独渲染
            }
        }
    }

    .content-container {
        background: white;
        overflow: hidden;

        .list-container {
            overflow-y: auto;
            .list-item {
                margin-bottom: 12px;
                border-radius: 8px;
                overflow: hidden;
                background: #fff;
                border-bottom: 12px solid var(--border);
                &:last-child {
                    margin-bottom: 0;
                }
            }

            .load-more {
                text-align: center;
                padding: 20px 0;

                .loading-indicator {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 8px;
                    color: #666;
                    font-size: 14px;

                    .el-icon {
                        font-size: 14px;
                    }
                }

                .no-more {
                    color: #999;
                    font-size: 14px;
                }
            }

            .empty {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                padding: 60px 20px;
                color: #999;

                img {
                    width: 120px;
                    height: 120px;
                    margin-bottom: 16px;
                    opacity: 0.6;
                }

                .text {
                    font-size: 14px;
                    line-height: 1.5;
                    text-align: center;
                }
            }

            .loading {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                padding: 60px 20px;
                color: #666;

                .el-icon {
                    font-size: 24px;
                    margin-bottom: 12px;
                }

                span {
                    font-size: 14px;
                }
            }
        }
    }
}
</style>
